const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');

async function testService() {
  try {
    console.log('正在启动5G网络服务测试...');
    
    const app = await NestFactory.create(AppModule);
    
    app.enableCors({
      origin: true,
      credentials: true,
    });

    app.setGlobalPrefix('api/v1');

    const port = process.env.PORT || 3015;
    await app.listen(port);
    
    console.log(`✅ 5G网络服务测试成功！服务已启动在端口: ${port}`);
    console.log(`🌐 API地址: http://localhost:${port}/api/v1`);
    console.log(`📚 API文档: http://localhost:${port}/api/docs`);
    
    // 测试基本功能
    setTimeout(async () => {
      try {
        const response = await fetch(`http://localhost:${port}/api/v1/network/performance`);
        if (response.ok) {
          const data = await response.json();
          console.log('✅ 性能指标API测试成功:', data);
        } else {
          console.log('❌ 性能指标API测试失败');
        }
      } catch (error) {
        console.log('❌ API测试错误:', error.message);
      }
    }, 2000);
    
  } catch (error) {
    console.error('❌ 5G网络服务启动失败:', error);
    process.exit(1);
  }
}

testService();
