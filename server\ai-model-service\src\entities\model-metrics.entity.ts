/**
 * 模型指标实体
 */
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsObject, Min, Max } from 'class-validator';
import { AIModel } from './ai-model.entity';

@Entity('model_metrics')
@Index(['modelId', 'metricDate'])
@Index(['metricType', 'metricDate'])
export class ModelMetrics {
  @ApiProperty({ description: '指标ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '模型ID' })
  @Column({ name: 'model_id' })
  modelId: string;

  @ApiProperty({ description: '指标类型' })
  @Column({ length: 50 })
  @IsString()
  metricType: string;

  @ApiProperty({ description: '指标名称' })
  @Column({ length: 100 })
  @IsString()
  metricName: string;

  @ApiProperty({ description: '指标值' })
  @Column({ type: 'float' })
  @IsNumber()
  value: number;

  @ApiProperty({ description: '指标单位' })
  @Column({ length: 20, nullable: true })
  @IsOptional()
  @IsString()
  unit?: string;

  @ApiProperty({ description: '指标描述' })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '指标标签' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsObject()
  labels?: Record<string, string>;

  @ApiProperty({ description: '指标元数据' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty({ description: '聚合类型' })
  @Column({ length: 20, nullable: true })
  @IsOptional()
  @IsString()
  aggregationType?: string; // sum, avg, min, max, count

  @ApiProperty({ description: '时间窗口(秒)' })
  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(1)
  timeWindow?: number;

  @ApiProperty({ description: '样本数量' })
  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(1)
  sampleCount?: number;

  @ApiProperty({ description: '最小值' })
  @Column({ type: 'float', nullable: true })
  @IsOptional()
  @IsNumber()
  minValue?: number;

  @ApiProperty({ description: '最大值' })
  @Column({ type: 'float', nullable: true })
  @IsOptional()
  @IsNumber()
  maxValue?: number;

  @ApiProperty({ description: '平均值' })
  @Column({ type: 'float', nullable: true })
  @IsOptional()
  @IsNumber()
  avgValue?: number;

  @ApiProperty({ description: '标准差' })
  @Column({ type: 'float', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  stdDev?: number;

  @ApiProperty({ description: '百分位数据' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsObject()
  percentiles?: {
    p50?: number;
    p90?: number;
    p95?: number;
    p99?: number;
  };

  @ApiProperty({ description: '指标日期' })
  @Column({ type: 'date', name: 'metric_date' })
  metricDate: Date;

  @ApiProperty({ description: '指标时间戳' })
  @Column({ type: 'timestamp', name: 'metric_timestamp' })
  metricTimestamp: Date;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => AIModel, model => model.metrics, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'model_id' })
  model: AIModel;
}
