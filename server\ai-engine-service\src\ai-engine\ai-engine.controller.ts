import { Controller, Get, Post, Delete, Body, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { 
  AIEngineService, 
  AIModel, 
  PredictionRequest, 
  PredictionResult,
  ModelPerformance 
} from './ai-engine.service';

@ApiTags('ai-engine')
@Controller('ai-engine')
export class AIEngineController {
  private readonly logger = new Logger(AIEngineController.name);

  constructor(private readonly aiEngineService: AIEngineService) {}

  @Get('performance')
  @ApiOperation({ summary: '获取AI引擎性能指标' })
  @ApiResponse({ status: 200, description: '成功获取性能指标' })
  getPerformanceMetrics() {
    return this.aiEngineService.getPerformanceMetrics();
  }

  @Get('models')
  @ApiOperation({ summary: '获取所有AI模型' })
  @ApiResponse({ status: 200, description: '成功获取模型列表' })
  getAllModels(): AIModel[] {
    return this.aiEngineService.getAllModels();
  }

  @Get('models/:modelId')
  @ApiOperation({ summary: '获取指定AI模型' })
  @ApiParam({ name: 'modelId', description: '模型ID' })
  @ApiResponse({ status: 200, description: '成功获取模型信息' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  getModel(@Param('modelId') modelId: string): AIModel {
    const model = this.aiEngineService.getModel(modelId);
    if (!model) {
      throw new Error(`模型不存在: ${modelId}`);
    }
    return model;
  }

  @Post('models')
  @ApiOperation({ summary: '创建AI模型' })
  @ApiBody({ description: '模型配置' })
  @ApiResponse({ status: 201, description: '成功创建模型' })
  async createModel(@Body() modelConfig: any): Promise<AIModel> {
    return await this.aiEngineService.createModel(modelConfig);
  }

  @Post('models/:modelId/train')
  @ApiOperation({ summary: '训练AI模型' })
  @ApiParam({ name: 'modelId', description: '模型ID' })
  @ApiBody({ description: '训练数据' })
  @ApiResponse({ status: 200, description: '成功训练模型' })
  async trainModel(
    @Param('modelId') modelId: string,
    @Body() trainingData: { data: number[][], labels: number[] }
  ): Promise<AIModel> {
    return await this.aiEngineService.trainModel(modelId, trainingData.data, trainingData.labels);
  }

  @Post('models/:modelId/predict')
  @ApiOperation({ summary: '模型预测' })
  @ApiParam({ name: 'modelId', description: '模型ID' })
  @ApiBody({ description: '预测请求' })
  @ApiResponse({ status: 200, description: '成功进行预测' })
  async predict(@Param('modelId') modelId: string, @Body() inputData: any): Promise<PredictionResult> {
    const request: PredictionRequest = {
      modelId,
      inputData: inputData.data,
      batchSize: inputData.batchSize
    };
    return await this.aiEngineService.predict(request);
  }

  @Post('models/:modelId/evaluate')
  @ApiOperation({ summary: '评估模型性能' })
  @ApiParam({ name: 'modelId', description: '模型ID' })
  @ApiBody({ description: '测试数据' })
  @ApiResponse({ status: 200, description: '成功评估模型' })
  async evaluateModel(
    @Param('modelId') modelId: string,
    @Body() testData: { data: number[][], labels: number[] }
  ): Promise<ModelPerformance> {
    return await this.aiEngineService.evaluateModel(modelId, testData.data, testData.labels);
  }

  @Post('models/:modelId/deploy')
  @ApiOperation({ summary: '部署AI模型' })
  @ApiParam({ name: 'modelId', description: '模型ID' })
  @ApiResponse({ status: 200, description: '成功部署模型' })
  async deployModel(@Param('modelId') modelId: string): Promise<AIModel> {
    return await this.aiEngineService.deployModel(modelId);
  }

  @Delete('models/:modelId')
  @ApiOperation({ summary: '删除AI模型' })
  @ApiParam({ name: 'modelId', description: '模型ID' })
  @ApiResponse({ status: 200, description: '成功删除模型' })
  async deleteModel(@Param('modelId') modelId: string) {
    const result = await this.aiEngineService.deleteModel(modelId);
    return { success: result };
  }
}
