/**
 * 模型版本实体
 */
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsBoolean, IsOptional, IsObject, Min } from 'class-validator';
import { AIModel } from './ai-model.entity';

@Entity('model_versions')
@Index(['modelId', 'version'], { unique: true })
export class ModelVersion {
  @ApiProperty({ description: '版本ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '模型ID' })
  @Column({ name: 'model_id' })
  modelId: string;

  @ApiProperty({ description: '版本号' })
  @Column({ length: 50 })
  @IsString()
  version: string;

  @ApiProperty({ description: '版本描述' })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '文件路径' })
  @Column({ length: 500 })
  @IsString()
  filePath: string;

  @ApiProperty({ description: '文件大小(MB)' })
  @Column({ type: 'bigint' })
  @IsNumber()
  @Min(0)
  fileSize: number;

  @ApiProperty({ description: '文件哈希值' })
  @Column({ length: 64, nullable: true })
  @IsOptional()
  @IsString()
  fileHash?: string;

  @ApiProperty({ description: '是否为当前版本' })
  @Column({ default: false })
  @IsBoolean()
  isCurrent: boolean;

  @ApiProperty({ description: '是否为稳定版本' })
  @Column({ default: false })
  @IsBoolean()
  isStable: boolean;

  @ApiProperty({ description: '版本配置' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @ApiProperty({ description: '性能指标' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsObject()
  performanceMetrics?: {
    accuracy?: number;
    precision?: number;
    recall?: number;
    f1Score?: number;
    inferenceTime?: number;
    memoryUsage?: number;
  };

  @ApiProperty({ description: '变更日志' })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  changelog?: string;

  @ApiProperty({ description: '发布者' })
  @Column({ length: 100, nullable: true })
  @IsOptional()
  @IsString()
  releasedBy?: string;

  @ApiProperty({ description: '发布时间' })
  @CreateDateColumn({ name: 'released_at' })
  releasedAt: Date;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => AIModel, model => model.versions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'model_id' })
  model: AIModel;
}
