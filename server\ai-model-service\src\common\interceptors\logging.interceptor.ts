/**
 * 日志拦截器
 * 记录请求和响应的详细信息
 */
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    const { method, url, body, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const userId = (request as any).user?.id || 'anonymous';
    const requestId = headers['x-request-id'] || this.generateRequestId();

    const startTime = Date.now();

    // 记录请求开始
    this.logger.log(
      `[${requestId}] [${userId}] ${method} ${url} ${ip} ${userAgent} - 请求开始`,
    );

    // 在开发环境下记录请求体
    if (process.env.NODE_ENV === 'development' && body && Object.keys(body).length > 0) {
      this.logger.debug(
        `[${requestId}] 请求体: ${JSON.stringify(body, null, 2)}`,
      );
    }

    return next.handle().pipe(
      tap((data) => {
        const responseTime = Date.now() - startTime;
        const statusCode = response.statusCode;
        
        this.logger.log(
          `[${requestId}] [${userId}] ${method} ${url} ${statusCode} ${responseTime}ms - 请求完成`,
        );

        // 在开发环境下记录响应数据（限制长度）
        if (process.env.NODE_ENV === 'development' && data) {
          const responseData = typeof data === 'object' 
            ? JSON.stringify(data).substring(0, 200) + '...'
            : String(data).substring(0, 200) + '...';
          
          this.logger.debug(
            `[${requestId}] 响应数据: ${responseData}`,
          );
        }
      }),
      catchError((error) => {
        const responseTime = Date.now() - startTime;
        const statusCode = error.status || 500;
        
        this.logger.error(
          `[${requestId}] [${userId}] ${method} ${url} ${statusCode} ${responseTime}ms - 请求失败: ${error.message}`,
          error.stack,
        );
        
        throw error;
      }),
    );
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
