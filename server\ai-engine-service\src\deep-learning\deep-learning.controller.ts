import { Controller, Get, Post, Delete, Body, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { DeepLearningService, DeepLearningNetwork } from './deep-learning.service';

@ApiTags('deep-learning')
@Controller('deep-learning')
export class DeepLearningController {
  private readonly logger = new Logger(DeepLearningController.name);

  constructor(private readonly deepLearningService: DeepLearningService) {}

  @Get('performance')
  @ApiOperation({ summary: '获取深度学习性能指标' })
  @ApiResponse({ status: 200, description: '成功获取性能指标' })
  getPerformanceMetrics() {
    return this.deepLearningService.getPerformanceMetrics();
  }

  @Get('networks')
  @ApiOperation({ summary: '获取所有深度学习网络' })
  @ApiResponse({ status: 200, description: '成功获取网络列表' })
  getAllNetworks(): DeepLearningNetwork[] {
    return this.deepLearningService.getAllNetworks();
  }

  @Get('networks/:networkId')
  @ApiOperation({ summary: '获取指定深度学习网络' })
  @ApiParam({ name: 'networkId', description: '网络ID' })
  @ApiResponse({ status: 200, description: '成功获取网络信息' })
  getNetwork(@Param('networkId') networkId: string): DeepLearningNetwork {
    const network = this.deepLearningService.getNetwork(networkId);
    if (!network) {
      throw new Error(`网络不存在: ${networkId}`);
    }
    return network;
  }

  @Post('networks')
  @ApiOperation({ summary: '创建深度学习网络' })
  @ApiBody({ description: '网络配置' })
  @ApiResponse({ status: 201, description: '成功创建网络' })
  async createNetwork(@Body() networkConfig: any): Promise<DeepLearningNetwork> {
    return await this.deepLearningService.createNetwork(networkConfig);
  }

  @Post('networks/:networkId/train')
  @ApiOperation({ summary: '训练深度学习网络' })
  @ApiParam({ name: 'networkId', description: '网络ID' })
  @ApiBody({ description: '训练数据' })
  @ApiResponse({ status: 200, description: '成功训练网络' })
  async trainNetwork(
    @Param('networkId') networkId: string,
    @Body() trainingData: { data: number[][], labels: number[] }
  ): Promise<DeepLearningNetwork> {
    return await this.deepLearningService.trainNetwork(networkId, trainingData.data, trainingData.labels);
  }

  @Delete('networks/:networkId')
  @ApiOperation({ summary: '删除深度学习网络' })
  @ApiParam({ name: 'networkId', description: '网络ID' })
  @ApiResponse({ status: 200, description: '成功删除网络' })
  async deleteNetwork(@Param('networkId') networkId: string) {
    const result = await this.deepLearningService.deleteNetwork(networkId);
    return { success: result };
  }
}
