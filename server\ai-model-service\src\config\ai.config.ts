/**
 * AI配置
 */
import { registerAs } from '@nestjs/config';

export const aiConfig = registerAs('ai', () => ({
  // 模型存储配置
  modelStorage: {
    basePath: process.env.AI_MODEL_STORAGE_PATH || './storage/models',
    maxFileSize: parseInt(process.env.AI_MODEL_MAX_FILE_SIZE, 10) || 5 * 1024 * 1024 * 1024, // 5GB
    allowedFormats: ['bin', 'onnx', 'pb', 'pth', 'h5', 'tflite', 'safetensors'],
  },

  // 推理配置
  inference: {
    maxConcurrentRequests: parseInt(process.env.AI_MAX_CONCURRENT_REQUESTS, 10) || 10,
    defaultTimeout: parseInt(process.env.AI_INFERENCE_TIMEOUT, 10) || 30000, // 30秒
    maxBatchSize: parseInt(process.env.AI_MAX_BATCH_SIZE, 10) || 32,
    enableGPU: process.env.AI_ENABLE_GPU === 'true',
    gpuMemoryLimit: parseInt(process.env.AI_GPU_MEMORY_LIMIT, 10) || 4096, // 4GB
  },

  // 缓存配置
  cache: {
    enableModelCache: process.env.AI_ENABLE_MODEL_CACHE !== 'false',
    modelCacheTTL: parseInt(process.env.AI_MODEL_CACHE_TTL, 10) || 3600, // 1小时
    resultCacheTTL: parseInt(process.env.AI_RESULT_CACHE_TTL, 10) || 300, // 5分钟
    maxCacheSize: parseInt(process.env.AI_MAX_CACHE_SIZE, 10) || 1024, // 1GB
  },

  // 监控配置
  monitoring: {
    enableMetrics: process.env.AI_ENABLE_METRICS !== 'false',
    metricsInterval: parseInt(process.env.AI_METRICS_INTERVAL, 10) || 60000, // 1分钟
    enablePerformanceLogging: process.env.AI_ENABLE_PERFORMANCE_LOGGING === 'true',
    alertThresholds: {
      responseTime: parseInt(process.env.AI_ALERT_RESPONSE_TIME, 10) || 5000, // 5秒
      errorRate: parseFloat(process.env.AI_ALERT_ERROR_RATE) || 0.05, // 5%
      memoryUsage: parseFloat(process.env.AI_ALERT_MEMORY_USAGE) || 0.8, // 80%
    },
  },

  // 安全配置
  security: {
    enableModelValidation: process.env.AI_ENABLE_MODEL_VALIDATION !== 'false',
    enableInputSanitization: process.env.AI_ENABLE_INPUT_SANITIZATION !== 'false',
    maxInputSize: parseInt(process.env.AI_MAX_INPUT_SIZE, 10) || 10 * 1024 * 1024, // 10MB
    allowedOrigins: process.env.AI_ALLOWED_ORIGINS?.split(',') || ['*'],
  },

  // 队列配置
  queue: {
    enableQueue: process.env.AI_ENABLE_QUEUE !== 'false',
    maxQueueSize: parseInt(process.env.AI_MAX_QUEUE_SIZE, 10) || 1000,
    processingConcurrency: parseInt(process.env.AI_PROCESSING_CONCURRENCY, 10) || 5,
    retryAttempts: parseInt(process.env.AI_RETRY_ATTEMPTS, 10) || 3,
    retryDelay: parseInt(process.env.AI_RETRY_DELAY, 10) || 5000, // 5秒
  },
}));
