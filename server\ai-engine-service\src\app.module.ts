import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { AIEngineModule } from './ai-engine/ai-engine.module';
import { DeepLearningModule } from './deep-learning/deep-learning.module';
import { FederatedLearningModule } from './federated-learning/federated-learning.module';
import { AdaptiveLearningModule } from './adaptive-learning/adaptive-learning.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 调度模块
    ScheduleModule.forRoot(),
    
    // AI引擎模块
    AIEngineModule,
    DeepLearningModule,
    FederatedLearningModule,
    AdaptiveLearningModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
