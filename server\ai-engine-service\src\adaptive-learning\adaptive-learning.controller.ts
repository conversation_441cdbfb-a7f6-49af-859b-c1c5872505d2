import { <PERSON>, Get, Post, Body, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { AdaptiveLearningService, AdaptiveStrategy, AdaptiveEnvironment } from './adaptive-learning.service';

@ApiTags('adaptive-learning')
@Controller('adaptive-learning')
export class AdaptiveLearningController {
  private readonly logger = new Logger(AdaptiveLearningController.name);

  constructor(private readonly adaptiveLearningService: AdaptiveLearningService) {}

  @Get('performance')
  @ApiOperation({ summary: '获取自适应学习性能指标' })
  @ApiResponse({ status: 200, description: '成功获取性能指标' })
  getPerformanceMetrics() {
    return this.adaptiveLearningService.getPerformanceMetrics();
  }

  @Get('strategies')
  @ApiOperation({ summary: '获取所有自适应学习策略' })
  @ApiResponse({ status: 200, description: '成功获取策略列表' })
  getAllStrategies(): AdaptiveStrategy[] {
    return this.adaptiveLearningService.getAllStrategies();
  }

  @Get('environments')
  @ApiOperation({ summary: '获取所有自适应学习环境' })
  @ApiResponse({ status: 200, description: '成功获取环境列表' })
  getAllEnvironments(): AdaptiveEnvironment[] {
    return this.adaptiveLearningService.getAllEnvironments();
  }

  @Post('strategies')
  @ApiOperation({ summary: '创建自适应学习策略' })
  @ApiBody({ description: '策略配置' })
  @ApiResponse({ status: 201, description: '成功创建策略' })
  async createStrategy(@Body() strategyConfig: any): Promise<AdaptiveStrategy> {
    return await this.adaptiveLearningService.createStrategy(strategyConfig);
  }

  @Post('environments')
  @ApiOperation({ summary: '创建自适应学习环境' })
  @ApiBody({ description: '环境配置' })
  @ApiResponse({ status: 201, description: '成功创建环境' })
  async createEnvironment(@Body() environmentConfig: any): Promise<AdaptiveEnvironment> {
    return await this.adaptiveLearningService.createEnvironment(environmentConfig);
  }
}
