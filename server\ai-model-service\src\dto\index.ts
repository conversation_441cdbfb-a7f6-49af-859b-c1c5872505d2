/**
 * DTO 类型定义
 */
import { ModelType, ModelStatus } from '../entities/ai-model.entity';

export interface CreateModelDto {
  name: string;
  description?: string;
  type: ModelType;
  version: string;
  currentVersion: string;
  filePath: string;
  config?: Record<string, any>;
  hardwareRequirements?: Record<string, any>;
  tags?: string[];
}

export interface UpdateModelDto {
  name?: string;
  description?: string;
  config?: Record<string, any>;
  hardwareRequirements?: Record<string, any>;
  tags?: string[];
  isActive?: boolean;
}

export interface InferenceRequestDto {
  inputData: any;
  parameters?: Record<string, any>;
  batchSize?: number;
}

export interface ModelQueryDto {
  page?: number;
  limit?: number;
  type?: ModelType;
  status?: ModelStatus;
  search?: string;
  tags?: string[];
}
