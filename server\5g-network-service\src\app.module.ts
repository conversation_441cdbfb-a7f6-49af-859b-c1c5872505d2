import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { NetworkModule } from './network/network.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 调度模块
    ScheduleModule.forRoot(),
    
    // 网络模块
    NetworkModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
