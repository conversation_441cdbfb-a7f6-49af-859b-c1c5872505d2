import { Controller, Get, Post, Body, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { FederatedLearningService, FederatedNode, FederatedTask } from './federated-learning.service';

@ApiTags('federated-learning')
@Controller('federated-learning')
export class FederatedLearningController {
  private readonly logger = new Logger(FederatedLearningController.name);

  constructor(private readonly federatedLearningService: FederatedLearningService) {}

  @Get('performance')
  @ApiOperation({ summary: '获取联邦学习性能指标' })
  @ApiResponse({ status: 200, description: '成功获取性能指标' })
  getPerformanceMetrics() {
    return this.federatedLearningService.getPerformanceMetrics();
  }

  @Get('nodes')
  @ApiOperation({ summary: '获取所有联邦学习节点' })
  @ApiResponse({ status: 200, description: '成功获取节点列表' })
  getAllNodes(): FederatedNode[] {
    return this.federatedLearningService.getAllNodes();
  }

  @Get('tasks')
  @ApiOperation({ summary: '获取所有联邦学习任务' })
  @ApiResponse({ status: 200, description: '成功获取任务列表' })
  getAllTasks(): FederatedTask[] {
    return this.federatedLearningService.getAllTasks();
  }

  @Post('nodes')
  @ApiOperation({ summary: '创建联邦学习节点' })
  @ApiBody({ description: '节点配置' })
  @ApiResponse({ status: 201, description: '成功创建节点' })
  async createNode(@Body() nodeConfig: any): Promise<FederatedNode> {
    return await this.federatedLearningService.createNode(nodeConfig);
  }

  @Post('tasks')
  @ApiOperation({ summary: '创建联邦学习任务' })
  @ApiBody({ description: '任务配置' })
  @ApiResponse({ status: 201, description: '成功创建任务' })
  async createTask(@Body() taskConfig: any): Promise<FederatedTask> {
    return await this.federatedLearningService.createTask(taskConfig);
  }
}
