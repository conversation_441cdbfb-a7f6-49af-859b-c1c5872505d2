console.log('开始测试5G网络服务...');

try {
  console.log('正在导入模块...');
  const { NestFactory } = require('@nestjs/core');
  console.log('NestFactory导入成功');
  
  const { AppModule } = require('./dist/app.module');
  console.log('AppModule导入成功');
  
  async function test() {
    try {
      console.log('正在创建应用...');
      const app = await NestFactory.create(AppModule, {
        logger: ['error', 'warn', 'log']
      });
      console.log('应用创建成功');
      
      const port = 3015;
      console.log(`正在启动服务，端口: ${port}`);
      await app.listen(port);
      
      console.log(`✅ 5G网络服务启动成功！`);
      console.log(`🌐 API地址: http://localhost:${port}/api/v1`);
      console.log(`📚 API文档: http://localhost:${port}/api/docs`);
      
    } catch (error) {
      console.error('❌ 服务启动失败:', error);
      process.exit(1);
    }
  }
  
  test();
  
} catch (error) {
  console.error('❌ 模块导入失败:', error);
  process.exit(1);
}
