/**
 * 推理日志实体
 */
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsBoolean, IsOptional, IsObject, IsEnum, Min } from 'class-validator';
import { AIModel } from './ai-model.entity';

// 推理状态枚举
export enum InferenceStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  TIMEOUT = 'timeout',
  CANCELLED = 'cancelled'
}

@Entity('inference_logs')
@Index(['modelId', 'createdAt'])
@Index(['status', 'createdAt'])
@Index(['userId', 'createdAt'])
export class InferenceLog {
  @ApiProperty({ description: '日志ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '模型ID' })
  @Column({ name: 'model_id' })
  modelId: string;

  @ApiProperty({ description: '请求ID' })
  @Column({ length: 100, nullable: true })
  @IsOptional()
  @IsString()
  requestId?: string;

  @ApiProperty({ description: '用户ID' })
  @Column({ length: 100, nullable: true })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({ description: '推理状态', enum: InferenceStatus })
  @Column({ type: 'enum', enum: InferenceStatus, default: InferenceStatus.PENDING })
  @IsEnum(InferenceStatus)
  status: InferenceStatus;

  @ApiProperty({ description: '输入数据' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsObject()
  inputData?: Record<string, any>;

  @ApiProperty({ description: '输出数据' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsObject()
  outputData?: Record<string, any>;

  @ApiProperty({ description: '推理参数' })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsObject()
  parameters?: Record<string, any>;

  @ApiProperty({ description: '响应时间(ms)' })
  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  responseTime?: number;

  @ApiProperty({ description: '处理开始时间' })
  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  startTime?: Date;

  @ApiProperty({ description: '处理结束时间' })
  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  endTime?: Date;

  @ApiProperty({ description: '错误信息' })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  errorMessage?: string;

  @ApiProperty({ description: '错误堆栈' })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  errorStack?: string;

  @ApiProperty({ description: '内存使用量(MB)' })
  @Column({ type: 'float', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  memoryUsage?: number;

  @ApiProperty({ description: 'CPU使用率(%)' })
  @Column({ type: 'float', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  cpuUsage?: number;

  @ApiProperty({ description: 'GPU使用率(%)' })
  @Column({ type: 'float', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  gpuUsage?: number;

  @ApiProperty({ description: '批次大小' })
  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(1)
  batchSize?: number;

  @ApiProperty({ description: '输入令牌数' })
  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  inputTokens?: number;

  @ApiProperty({ description: '输出令牌数' })
  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  outputTokens?: number;

  @ApiProperty({ description: '置信度分数' })
  @Column({ type: 'float', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  confidenceScore?: number;

  @ApiProperty({ description: '客户端IP' })
  @Column({ length: 45, nullable: true })
  @IsOptional()
  @IsString()
  clientIp?: string;

  @ApiProperty({ description: '用户代理' })
  @Column({ length: 500, nullable: true })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => AIModel, model => model.inferenceLogs, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'model_id' })
  model: AIModel;
}
