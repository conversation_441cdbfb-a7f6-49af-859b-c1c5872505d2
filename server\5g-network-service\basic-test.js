console.log('基础测试开始...');

try {
  console.log('测试Node.js版本:', process.version);
  console.log('当前工作目录:', process.cwd());
  
  console.log('测试require功能...');
  const fs = require('fs');
  console.log('fs模块加载成功');
  
  console.log('检查dist目录...');
  if (fs.existsSync('./dist')) {
    console.log('dist目录存在');
    const files = fs.readdirSync('./dist');
    console.log('dist目录内容:', files);
  } else {
    console.log('dist目录不存在');
  }
  
  console.log('测试@nestjs/core导入...');
  const nestjs = require('@nestjs/core');
  console.log('@nestjs/core导入成功');
  
  console.log('测试app.module导入...');
  const appModule = require('./dist/app.module');
  console.log('app.module导入成功');
  
  console.log('✅ 基础测试通过');
  
} catch (error) {
  console.error('❌ 基础测试失败:', error);
}
