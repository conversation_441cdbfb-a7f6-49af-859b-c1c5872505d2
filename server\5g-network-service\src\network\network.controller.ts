import { Controller, Get, Post, Delete, Body, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import {
  FiveGNetworkService,
  FiveGPrivateNetwork,
  NetworkSlice,
  ConnectedDevice,
  CoreNetworkElement
} from './5g-network.service';

@ApiTags('5g-network')
@Controller('network')
export class NetworkController {
  private readonly logger = new Logger(NetworkController.name);

  constructor(private readonly fiveGNetworkService: FiveGNetworkService) {}

  @Get('performance')
  @ApiOperation({ summary: '获取网络性能指标' })
  @ApiResponse({ status: 200, description: '成功获取性能指标' })
  getPerformanceMetrics() {
    return this.fiveGNetworkService.getPerformanceMetrics();
  }

  @Get('private-networks')
  @ApiOperation({ summary: '获取所有5G专网' })
  @ApiResponse({ status: 200, description: '成功获取专网列表' })
  getAllPrivateNetworks(): FiveGPrivateNetwork[] {
    return this.fiveGNetworkService.getAllPrivateNetworks();
  }

  @Get('private-networks/:networkId')
  @ApiOperation({ summary: '获取指定5G专网' })
  @ApiParam({ name: 'networkId', description: '网络ID' })
  @ApiResponse({ status: 200, description: '成功获取专网信息' })
  @ApiResponse({ status: 404, description: '专网不存在' })
  getPrivateNetwork(@Param('networkId') networkId: string): FiveGPrivateNetwork {
    const network = this.fiveGNetworkService.getPrivateNetwork(networkId);
    if (!network) {
      throw new Error(`专网不存在: ${networkId}`);
    }
    return network;
  }

  @Post('private-networks')
  @ApiOperation({ summary: '创建5G专网' })
  @ApiBody({ description: '专网配置' })
  @ApiResponse({ status: 201, description: '成功创建专网' })
  async createPrivateNetwork(@Body() networkConfig: any): Promise<FiveGPrivateNetwork> {
    return await this.fiveGNetworkService.createPrivateNetwork(networkConfig);
  }

  @Delete('private-networks/:networkId')
  @ApiOperation({ summary: '删除5G专网' })
  @ApiParam({ name: 'networkId', description: '网络ID' })
  @ApiResponse({ status: 200, description: '成功删除专网' })
  async deletePrivateNetwork(@Param('networkId') networkId: string) {
    const result = await this.fiveGNetworkService.deletePrivateNetwork(networkId);
    return { success: result };
  }

  @Get('slices')
  @ApiOperation({ summary: '获取所有网络切片' })
  @ApiResponse({ status: 200, description: '成功获取切片列表' })
  getAllNetworkSlices(): NetworkSlice[] {
    return this.fiveGNetworkService.getAllNetworkSlices();
  }

  @Get('slices/:sliceId')
  @ApiOperation({ summary: '获取指定网络切片' })
  @ApiParam({ name: 'sliceId', description: '切片ID' })
  @ApiResponse({ status: 200, description: '成功获取切片信息' })
  getNetworkSlice(@Param('sliceId') sliceId: string): NetworkSlice {
    const slice = this.fiveGNetworkService.getNetworkSlice(sliceId);
    if (!slice) {
      throw new Error(`网络切片不存在: ${sliceId}`);
    }
    return slice;
  }

  @Post('private-networks/:networkId/slices')
  @ApiOperation({ summary: '创建网络切片' })
  @ApiParam({ name: 'networkId', description: '网络ID' })
  @ApiBody({ description: '切片配置' })
  @ApiResponse({ status: 201, description: '成功创建切片' })
  async createNetworkSlice(@Param('networkId') networkId: string, @Body() sliceConfig: any): Promise<NetworkSlice> {
    return await this.fiveGNetworkService.createNetworkSlice(networkId, sliceConfig);
  }

  @Delete('slices/:sliceId')
  @ApiOperation({ summary: '删除网络切片' })
  @ApiParam({ name: 'sliceId', description: '切片ID' })
  @ApiResponse({ status: 200, description: '成功删除切片' })
  async deleteNetworkSlice(@Param('sliceId') sliceId: string) {
    const result = await this.fiveGNetworkService.deleteNetworkSlice(sliceId);
    return { success: result };
  }

  @Get('devices')
  @ApiOperation({ summary: '获取所有连接设备' })
  @ApiResponse({ status: 200, description: '成功获取设备列表' })
  getAllConnectedDevices(): ConnectedDevice[] {
    return this.fiveGNetworkService.getAllConnectedDevices();
  }

  @Get('devices/:deviceId')
  @ApiOperation({ summary: '获取指定连接设备' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiResponse({ status: 200, description: '成功获取设备信息' })
  getConnectedDevice(@Param('deviceId') deviceId: string): ConnectedDevice {
    const device = this.fiveGNetworkService.getConnectedDevice(deviceId);
    if (!device) {
      throw new Error(`设备不存在: ${deviceId}`);
    }
    return device;
  }

  @Post('private-networks/:networkId/slices/:sliceId/devices')
  @ApiOperation({ summary: '连接设备到网络' })
  @ApiParam({ name: 'networkId', description: '网络ID' })
  @ApiParam({ name: 'sliceId', description: '切片ID' })
  @ApiBody({ description: '设备配置' })
  @ApiResponse({ status: 201, description: '成功连接设备' })
  async connectDevice(
    @Param('networkId') networkId: string,
    @Param('sliceId') sliceId: string,
    @Body() deviceConfig: any
  ): Promise<ConnectedDevice> {
    return await this.fiveGNetworkService.connectDevice(networkId, sliceId, deviceConfig);
  }

  @Delete('devices/:deviceId')
  @ApiOperation({ summary: '断开设备连接' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiResponse({ status: 200, description: '成功断开设备' })
  async disconnectDevice(@Param('deviceId') deviceId: string) {
    const result = await this.fiveGNetworkService.disconnectDevice(deviceId);
    return { success: result };
  }

  @Get('network-functions')
  @ApiOperation({ summary: '获取所有网络功能' })
  @ApiResponse({ status: 200, description: '成功获取网络功能列表' })
  getAllNetworkFunctions(): CoreNetworkElement[] {
    return this.fiveGNetworkService.getAllNetworkFunctions();
  }

  @Post('network-functions')
  @ApiOperation({ summary: '添加网络功能' })
  @ApiBody({ description: '网络功能配置' })
  @ApiResponse({ status: 201, description: '成功添加网络功能' })
  async addNetworkFunction(@Body() functionConfig: any): Promise<CoreNetworkElement> {
    return await this.fiveGNetworkService.addNetworkFunction(functionConfig);
  }

  @Delete('network-functions/:elementId')
  @ApiOperation({ summary: '删除网络功能' })
  @ApiParam({ name: 'elementId', description: '网络功能ID' })
  @ApiResponse({ status: 200, description: '成功删除网络功能' })
  async deleteNetworkFunction(@Param('elementId') elementId: string) {
    const result = await this.fiveGNetworkService.deleteNetworkFunction(elementId);
    return { success: result };
  }
}
