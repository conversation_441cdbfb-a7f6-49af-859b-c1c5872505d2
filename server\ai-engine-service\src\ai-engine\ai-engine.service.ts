import { Injectable, Logger } from '@nestjs/common';
import { Matrix } from 'ml-matrix';
import { SimpleLinearRegression, PolynomialRegression } from 'ml-regression';
import * as ss from 'simple-statistics';
import * as math from 'mathjs';

/**
 * AI模型类型枚举
 */
export enum ModelType {
  LINEAR_REGRESSION = 'linear_regression',
  POLYNOMIAL_REGRESSION = 'polynomial_regression',
  NEURAL_NETWORK = 'neural_network',
  DECISION_TREE = 'decision_tree',
  RANDOM_FOREST = 'random_forest',
  SVM = 'svm',
  CLUSTERING = 'clustering',
  DEEP_LEARNING = 'deep_learning'
}

/**
 * 训练状态枚举
 */
export enum TrainingStatus {
  PENDING = 'pending',
  TRAINING = 'training',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

/**
 * AI模型接口
 */
export interface AIModel {
  modelId: string;
  name: string;
  type: ModelType;
  version: string;
  description: string;
  parameters: ModelParameters;
  performance: ModelPerformance;
  trainingData: TrainingDataInfo;
  status: TrainingStatus;
  createdAt: Date;
  updatedAt: Date;
  deployedAt?: Date;
}

/**
 * 模型参数接口
 */
export interface ModelParameters {
  learningRate?: number;
  epochs?: number;
  batchSize?: number;
  hiddenLayers?: number[];
  activationFunction?: string;
  optimizer?: string;
  lossFunction?: string;
  regularization?: {
    type: 'l1' | 'l2' | 'dropout';
    value: number;
  };
  [key: string]: any;
}

/**
 * 模型性能接口
 */
export interface ModelPerformance {
  accuracy?: number;
  precision?: number;
  recall?: number;
  f1Score?: number;
  mse?: number;
  rmse?: number;
  mae?: number;
  r2Score?: number;
  trainingTime: number; // 毫秒
  inferenceTime: number; // 毫秒
  modelSize: number; // 字节
}

/**
 * 训练数据信息接口
 */
export interface TrainingDataInfo {
  datasetId: string;
  datasetName: string;
  sampleCount: number;
  featureCount: number;
  targetVariable: string;
  dataTypes: { [key: string]: string };
  preprocessingSteps: string[];
}

/**
 * 预测请求接口
 */
export interface PredictionRequest {
  modelId: string;
  inputData: number[][] | number[];
  batchSize?: number;
}

/**
 * 预测结果接口
 */
export interface PredictionResult {
  predictions: number[] | number[][];
  confidence?: number[];
  processingTime: number;
  modelVersion: string;
}

/**
 * AI引擎服务
 */
@Injectable()
export class AIEngineService {
  private readonly logger = new Logger(AIEngineService.name);

  // AI模型存储
  private models: Map<string, AIModel> = new Map();
  private trainedModels: Map<string, any> = new Map();

  // 性能监控
  private performanceMetrics = {
    totalModels: 0,
    activeModels: 0,
    totalPredictions: 0,
    averageInferenceTime: 0,
    totalTrainingTime: 0,
    successRate: 0
  };

  constructor() {
    this.initializeAIEngine();
    this.startPerformanceMonitoring();
  }

  /**
   * 初始化AI引擎
   */
  private initializeAIEngine(): void {
    this.logger.log('正在初始化AI引擎服务...');

    // 初始化性能指标
    this.performanceMetrics = {
      totalModels: 0,
      activeModels: 0,
      totalPredictions: 0,
      averageInferenceTime: 0,
      totalTrainingTime: 0,
      successRate: 0
    };

    this.logger.log('AI引擎服务初始化完成');
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    this.logger.log('启动AI引擎性能监控...');

    // 启动监控定时器
    setInterval(() => {
      this.updatePerformanceMetrics();
    }, 30000); // 每30秒更新一次性能指标

    this.logger.log('AI引擎性能监控已启动');
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(): void {
    this.performanceMetrics.totalModels = this.models.size;
    this.performanceMetrics.activeModels = Array.from(this.models.values())
      .filter(model => model.status === TrainingStatus.COMPLETED).length;

    // 计算平均推理时间
    const completedModels = Array.from(this.models.values())
      .filter(model => model.status === TrainingStatus.COMPLETED);
    
    if (completedModels.length > 0) {
      const totalInferenceTime = completedModels.reduce(
        (sum, model) => sum + model.performance.inferenceTime, 0
      );
      this.performanceMetrics.averageInferenceTime = totalInferenceTime / completedModels.length;

      const totalTrainingTime = completedModels.reduce(
        (sum, model) => sum + model.performance.trainingTime, 0
      );
      this.performanceMetrics.totalTrainingTime = totalTrainingTime;

      this.performanceMetrics.successRate = completedModels.length / this.models.size * 100;
    }
  }

  /**
   * 创建AI模型
   */
  async createModel(modelConfig: Partial<AIModel>): Promise<AIModel> {
    const modelId = `model_${Date.now()}`;

    const model: AIModel = {
      modelId,
      name: modelConfig.name || `AI模型_${modelId}`,
      type: modelConfig.type || ModelType.LINEAR_REGRESSION,
      version: '1.0.0',
      description: modelConfig.description || '智慧工厂AI模型',
      parameters: modelConfig.parameters || {
        learningRate: 0.01,
        epochs: 100,
        batchSize: 32
      },
      performance: {
        trainingTime: 0,
        inferenceTime: 0,
        modelSize: 0
      },
      trainingData: modelConfig.trainingData || {
        datasetId: 'default',
        datasetName: '默认数据集',
        sampleCount: 0,
        featureCount: 0,
        targetVariable: 'target',
        dataTypes: {},
        preprocessingSteps: []
      },
      status: TrainingStatus.PENDING,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.models.set(modelId, model);
    this.logger.log(`创建AI模型成功: ${modelId}`);

    return model;
  }

  /**
   * 训练模型
   */
  async trainModel(modelId: string, trainingData: number[][], labels: number[]): Promise<AIModel> {
    const model = this.models.get(modelId);
    if (!model) {
      throw new Error(`模型不存在: ${modelId}`);
    }

    const startTime = Date.now();
    model.status = TrainingStatus.TRAINING;
    model.updatedAt = new Date();

    try {
      let trainedModel: any;

      switch (model.type) {
        case ModelType.LINEAR_REGRESSION:
          trainedModel = await this.trainLinearRegression(trainingData, labels);
          break;
        case ModelType.POLYNOMIAL_REGRESSION:
          trainedModel = await this.trainPolynomialRegression(trainingData, labels);
          break;
        default:
          trainedModel = await this.trainLinearRegression(trainingData, labels);
      }

      const trainingTime = Date.now() - startTime;
      
      // 更新模型性能
      model.performance.trainingTime = trainingTime;
      model.performance.modelSize = JSON.stringify(trainedModel).length;
      model.status = TrainingStatus.COMPLETED;
      model.updatedAt = new Date();

      // 存储训练好的模型
      this.trainedModels.set(modelId, trainedModel);

      this.logger.log(`模型训练完成: ${modelId}, 耗时: ${trainingTime}ms`);

      return model;
    } catch (error) {
      model.status = TrainingStatus.FAILED;
      model.updatedAt = new Date();
      this.logger.error(`模型训练失败: ${modelId}`, error);
      throw error;
    }
  }

  /**
   * 训练线性回归模型
   */
  private async trainLinearRegression(trainingData: number[][], labels: number[]): Promise<any> {
    // 使用第一个特征进行简单线性回归
    const x = trainingData.map(row => row[0]);
    const y = labels;

    const regression = new SimpleLinearRegression(x, y);
    return regression;
  }

  /**
   * 训练多项式回归模型
   */
  private async trainPolynomialRegression(trainingData: number[][], labels: number[]): Promise<any> {
    const x = trainingData.map(row => row[0]);
    const y = labels;

    const regression = new PolynomialRegression(x, y, 2); // 二次多项式
    return regression;
  }

  /**
   * 模型预测
   */
  async predict(request: PredictionRequest): Promise<PredictionResult> {
    const model = this.models.get(request.modelId);
    if (!model) {
      throw new Error(`模型不存在: ${request.modelId}`);
    }

    if (model.status !== TrainingStatus.COMPLETED) {
      throw new Error(`模型未完成训练: ${request.modelId}`);
    }

    const trainedModel = this.trainedModels.get(request.modelId);
    if (!trainedModel) {
      throw new Error(`训练模型不存在: ${request.modelId}`);
    }

    const startTime = Date.now();

    try {
      let predictions: number[] | number[][];

      if (Array.isArray(request.inputData[0])) {
        // 批量预测
        const batchData = request.inputData as number[][];
        predictions = batchData.map(row => trainedModel.predict(row[0]));
      } else {
        // 单个预测
        const singleData = request.inputData as number[];
        predictions = [trainedModel.predict(singleData[0])];
      }

      const processingTime = Date.now() - startTime;
      
      // 更新模型推理时间
      model.performance.inferenceTime = processingTime;
      this.performanceMetrics.totalPredictions++;

      this.logger.log(`模型预测完成: ${request.modelId}, 耗时: ${processingTime}ms`);

      return {
        predictions,
        processingTime,
        modelVersion: model.version
      };
    } catch (error) {
      this.logger.error(`模型预测失败: ${request.modelId}`, error);
      throw error;
    }
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      timestamp: new Date()
    };
  }

  /**
   * 获取所有模型
   */
  getAllModels(): AIModel[] {
    return Array.from(this.models.values());
  }

  /**
   * 获取指定模型
   */
  getModel(modelId: string): AIModel | undefined {
    return this.models.get(modelId);
  }

  /**
   * 删除模型
   */
  async deleteModel(modelId: string): Promise<boolean> {
    const model = this.models.get(modelId);
    if (!model) {
      return false;
    }

    this.models.delete(modelId);
    this.trainedModels.delete(modelId);
    
    this.logger.log(`删除AI模型: ${modelId}`);
    return true;
  }

  /**
   * 部署模型
   */
  async deployModel(modelId: string): Promise<AIModel> {
    const model = this.models.get(modelId);
    if (!model) {
      throw new Error(`模型不存在: ${modelId}`);
    }

    if (model.status !== TrainingStatus.COMPLETED) {
      throw new Error(`模型未完成训练，无法部署: ${modelId}`);
    }

    model.deployedAt = new Date();
    model.updatedAt = new Date();

    this.logger.log(`模型部署成功: ${modelId}`);
    return model;
  }

  /**
   * 评估模型性能
   */
  async evaluateModel(modelId: string, testData: number[][], testLabels: number[]): Promise<ModelPerformance> {
    const model = this.models.get(modelId);
    if (!model) {
      throw new Error(`模型不存在: ${modelId}`);
    }

    const trainedModel = this.trainedModels.get(modelId);
    if (!trainedModel) {
      throw new Error(`训练模型不存在: ${modelId}`);
    }

    const startTime = Date.now();

    // 进行预测
    const predictions = testData.map(row => trainedModel.predict(row[0]));

    // 计算性能指标
    const mse = ss.meanSquaredError(testLabels, predictions);
    const rmse = Math.sqrt(mse);
    const mae = ss.meanAbsoluteError(testLabels, predictions);
    const r2 = ss.rSquared(testLabels, predictions);

    const evaluationTime = Date.now() - startTime;

    const performance: ModelPerformance = {
      mse,
      rmse,
      mae,
      r2Score: r2,
      trainingTime: model.performance.trainingTime,
      inferenceTime: evaluationTime / testData.length,
      modelSize: model.performance.modelSize
    };

    // 更新模型性能
    model.performance = { ...model.performance, ...performance };
    model.updatedAt = new Date();

    this.logger.log(`模型评估完成: ${modelId}`);
    return performance;
  }
}
